// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i36;
import 'package:flutter/material.dart' as _i37;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i13;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i24;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i38;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/availability_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/form_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/fqpd_page.dart'
    as _i6;
import 'package:storetrack_app/features/home/<USER>/pages/history_page.dart'
    as _i8;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/induction_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/leave_page.dart'
    as _i12;
import 'package:storetrack_app/features/home/<USER>/pages/more_holder_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/pages/mpt_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i19;
import 'package:storetrack_app/features/home/<USER>/pages/profile_holder_page.dart'
    as _i20;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i21;
import 'package:storetrack_app/features/home/<USER>/pages/qpmd_page.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart'
    as _i23;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i25;
import 'package:storetrack_app/features/home/<USER>/pages/skills_page.dart'
    as _i26;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i28;
import 'package:storetrack_app/features/home/<USER>/pages/store_info_page.dart'
    as _i29;
import 'package:storetrack_app/features/home/<USER>/pages/sub_header_page.dart'
    as _i30;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i31;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i32;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i33;
import 'package:storetrack_app/features/home/<USER>/pages/useful_links_page.dart'
    as _i34;
import 'package:storetrack_app/features/notification/presentation/pages/notification_page.dart'
    as _i18;
import 'package:storetrack_app/features/profile/presentation/pages/edit_profile_page.dart'
    as _i5;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i27;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i35;

/// generated route for
/// [_i1.AssistantPage]
class AssistantRoute extends _i36.PageRouteInfo<void> {
  const AssistantRoute({List<_i36.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i1.AssistantPage();
    },
  );
}

/// generated route for
/// [_i2.AvailabilityPage]
class AvailabilityRoute extends _i36.PageRouteInfo<void> {
  const AvailabilityRoute({List<_i36.PageRouteInfo>? children})
      : super(
          AvailabilityRoute.name,
          initialChildren: children,
        );

  static const String name = 'AvailabilityRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i2.AvailabilityPage();
    },
  );
}

/// generated route for
/// [_i3.DashboardHolderPage]
class DashboardHolderRoute extends _i36.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i36.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i3.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i4.DashboardPage]
class DashboardRoute extends _i36.PageRouteInfo<void> {
  const DashboardRoute({List<_i36.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i4.DashboardPage();
    },
  );
}

/// generated route for
/// [_i5.EditProfilePage]
class EditProfileRoute extends _i36.PageRouteInfo<void> {
  const EditProfileRoute({List<_i36.PageRouteInfo>? children})
      : super(
          EditProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'EditProfileRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i5.EditProfilePage();
    },
  );
}

/// generated route for
/// [_i6.FQPDPage]
class FQPDRoute extends _i36.PageRouteInfo<FQPDRouteArgs> {
  FQPDRoute({
    _i37.Key? key,
    _i38.Question? question,
    num? taskId,
    num? formId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          FQPDRoute.name,
          args: FQPDRouteArgs(
            key: key,
            question: question,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'FQPDRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<FQPDRouteArgs>(orElse: () => const FQPDRouteArgs());
      return _i6.FQPDPage(
        key: args.key,
        question: args.question,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class FQPDRouteArgs {
  const FQPDRouteArgs({
    this.key,
    this.question,
    this.taskId,
    this.formId,
  });

  final _i37.Key? key;

  final _i38.Question? question;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'FQPDRouteArgs{key: $key, question: $question, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i7.FormPage]
class FormRoute extends _i36.PageRouteInfo<FormRouteArgs> {
  FormRoute({
    _i37.Key? key,
    required _i38.TaskDetail task,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          FormRoute.name,
          args: FormRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'FormRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FormRouteArgs>();
      return _i7.FormPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class FormRouteArgs {
  const FormRouteArgs({
    this.key,
    required this.task,
  });

  final _i37.Key? key;

  final _i38.TaskDetail task;

  @override
  String toString() {
    return 'FormRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i8.HistoryPage]
class HistoryRoute extends _i36.PageRouteInfo<void> {
  const HistoryRoute({List<_i36.PageRouteInfo>? children})
      : super(
          HistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'HistoryRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i8.HistoryPage();
    },
  );
}

/// generated route for
/// [_i9.HomePage]
class HomeRoute extends _i36.PageRouteInfo<void> {
  const HomeRoute({List<_i36.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i9.HomePage();
    },
  );
}

/// generated route for
/// [_i10.InductionPage]
class InductionRoute extends _i36.PageRouteInfo<void> {
  const InductionRoute({List<_i36.PageRouteInfo>? children})
      : super(
          InductionRoute.name,
          initialChildren: children,
        );

  static const String name = 'InductionRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i10.InductionPage();
    },
  );
}

/// generated route for
/// [_i11.JourneyMapPage]
class JourneyMapRoute extends _i36.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i36.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i11.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i12.LeavePage]
class LeaveRoute extends _i36.PageRouteInfo<void> {
  const LeaveRoute({List<_i36.PageRouteInfo>? children})
      : super(
          LeaveRoute.name,
          initialChildren: children,
        );

  static const String name = 'LeaveRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i12.LeavePage();
    },
  );
}

/// generated route for
/// [_i13.LoginPage]
class LoginRoute extends _i36.PageRouteInfo<void> {
  const LoginRoute({List<_i36.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i13.LoginPage();
    },
  );
}

/// generated route for
/// [_i14.MPTPage]
class MPTRoute extends _i36.PageRouteInfo<MPTRouteArgs> {
  MPTRoute({
    _i37.Key? key,
    String? taskId,
    String? formId,
    String? questionId,
    String? questionPartId,
    String? measurementId,
    String? combineTypeId,
    String? questionPartMultiId,
    List<String>? images,
    _i38.Question? question,
    int level = 2,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          MPTRoute.name,
          args: MPTRouteArgs(
            key: key,
            taskId: taskId,
            formId: formId,
            questionId: questionId,
            questionPartId: questionPartId,
            measurementId: measurementId,
            combineTypeId: combineTypeId,
            questionPartMultiId: questionPartMultiId,
            images: images,
            question: question,
            level: level,
          ),
          initialChildren: children,
        );

  static const String name = 'MPTRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<MPTRouteArgs>(orElse: () => const MPTRouteArgs());
      return _i14.MPTPage(
        key: args.key,
        taskId: args.taskId,
        formId: args.formId,
        questionId: args.questionId,
        questionPartId: args.questionPartId,
        measurementId: args.measurementId,
        combineTypeId: args.combineTypeId,
        questionPartMultiId: args.questionPartMultiId,
        images: args.images,
        question: args.question,
        level: args.level,
      );
    },
  );
}

class MPTRouteArgs {
  const MPTRouteArgs({
    this.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
    this.images,
    this.question,
    this.level = 2,
  });

  final _i37.Key? key;

  final String? taskId;

  final String? formId;

  final String? questionId;

  final String? questionPartId;

  final String? measurementId;

  final String? combineTypeId;

  final String? questionPartMultiId;

  final List<String>? images;

  final _i38.Question? question;

  final int level;

  @override
  String toString() {
    return 'MPTRouteArgs{key: $key, taskId: $taskId, formId: $formId, questionId: $questionId, questionPartId: $questionPartId, measurementId: $measurementId, combineTypeId: $combineTypeId, questionPartMultiId: $questionPartMultiId, images: $images, question: $question, level: $level}';
  }
}

/// generated route for
/// [_i15.MoreHolderPage]
class MoreHolderRoute extends _i36.PageRouteInfo<void> {
  const MoreHolderRoute({List<_i36.PageRouteInfo>? children})
      : super(
          MoreHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreHolderRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i15.MoreHolderPage();
    },
  );
}

/// generated route for
/// [_i16.MorePage]
class MoreRoute extends _i36.PageRouteInfo<void> {
  const MoreRoute({List<_i36.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i16.MorePage();
    },
  );
}

/// generated route for
/// [_i17.NotesPage]
class NotesRoute extends _i36.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i37.Key? key,
    required _i38.TaskDetail task,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i17.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i37.Key? key;

  final _i38.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i18.NotificationsPage]
class NotificationsRoute extends _i36.PageRouteInfo<void> {
  const NotificationsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i18.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i19.PosPage]
class PosRoute extends _i36.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i37.Key? key,
    _i38.TaskDetail? task,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i19.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i37.Key? key;

  final _i38.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i20.ProfileHolderPage]
class ProfileHolderRoute extends _i36.PageRouteInfo<void> {
  const ProfileHolderRoute({List<_i36.PageRouteInfo>? children})
      : super(
          ProfileHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileHolderRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i20.ProfileHolderPage();
    },
  );
}

/// generated route for
/// [_i21.ProfilePage]
class ProfileRoute extends _i36.PageRouteInfo<void> {
  const ProfileRoute({List<_i36.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i21.ProfilePage();
    },
  );
}

/// generated route for
/// [_i22.QPMDPage]
class QPMDRoute extends _i36.PageRouteInfo<QPMDRouteArgs> {
  QPMDRoute({
    _i37.Key? key,
    _i38.Question? question,
    _i38.QuestionPart? questionPart,
    num? taskId,
    num? formId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          QPMDRoute.name,
          args: QPMDRouteArgs(
            key: key,
            question: question,
            questionPart: questionPart,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'QPMDRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<QPMDRouteArgs>(orElse: () => const QPMDRouteArgs());
      return _i22.QPMDPage(
        key: args.key,
        question: args.question,
        questionPart: args.questionPart,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class QPMDRouteArgs {
  const QPMDRouteArgs({
    this.key,
    this.question,
    this.questionPart,
    this.taskId,
    this.formId,
  });

  final _i37.Key? key;

  final _i38.Question? question;

  final _i38.QuestionPart? questionPart;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'QPMDRouteArgs{key: $key, question: $question, questionPart: $questionPart, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i23.QuestionPage]
class QuestionRoute extends _i36.PageRouteInfo<QuestionRouteArgs> {
  QuestionRoute({
    _i37.Key? key,
    required _i38.Form form,
    num? taskId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          QuestionRoute.name,
          args: QuestionRouteArgs(
            key: key,
            form: form,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QuestionRouteArgs>();
      return _i23.QuestionPage(
        key: args.key,
        form: args.form,
        taskId: args.taskId,
      );
    },
  );
}

class QuestionRouteArgs {
  const QuestionRouteArgs({
    this.key,
    required this.form,
    this.taskId,
  });

  final _i37.Key? key;

  final _i38.Form form;

  final num? taskId;

  @override
  String toString() {
    return 'QuestionRouteArgs{key: $key, form: $form, taskId: $taskId}';
  }
}

/// generated route for
/// [_i24.ResetPasswordPage]
class ResetPasswordRoute extends _i36.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i37.Key? key,
    required String email,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i24.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i37.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i25.SchedulePage]
class ScheduleRoute extends _i36.PageRouteInfo<void> {
  const ScheduleRoute({List<_i36.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i25.SchedulePage();
    },
  );
}

/// generated route for
/// [_i26.SkillsPage]
class SkillsRoute extends _i36.PageRouteInfo<void> {
  const SkillsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          SkillsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SkillsRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i26.SkillsPage();
    },
  );
}

/// generated route for
/// [_i27.SplashPage]
class SplashRoute extends _i36.PageRouteInfo<void> {
  const SplashRoute({List<_i36.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i27.SplashPage();
    },
  );
}

/// generated route for
/// [_i28.StoreHistoryPage]
class StoreHistoryRoute extends _i36.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i37.Key? key,
    required int storeId,
    required int taskId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i28.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i37.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i29.StoreInfoPage]
class StoreInfoRoute extends _i36.PageRouteInfo<void> {
  const StoreInfoRoute({List<_i36.PageRouteInfo>? children})
      : super(
          StoreInfoRoute.name,
          initialChildren: children,
        );

  static const String name = 'StoreInfoRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i29.StoreInfoPage();
    },
  );
}

/// generated route for
/// [_i30.SubHeaderPage]
class SubHeaderRoute extends _i36.PageRouteInfo<SubHeaderRouteArgs> {
  SubHeaderRoute({
    _i37.Key? key,
    required String title,
    required List<_i38.QuestionPart> questionParts,
    required _i38.Question question,
    num? taskId,
    num? formId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          SubHeaderRoute.name,
          args: SubHeaderRouteArgs(
            key: key,
            title: title,
            questionParts: questionParts,
            question: question,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'SubHeaderRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SubHeaderRouteArgs>();
      return _i30.SubHeaderPage(
        key: args.key,
        title: args.title,
        questionParts: args.questionParts,
        question: args.question,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class SubHeaderRouteArgs {
  const SubHeaderRouteArgs({
    this.key,
    required this.title,
    required this.questionParts,
    required this.question,
    this.taskId,
    this.formId,
  });

  final _i37.Key? key;

  final String title;

  final List<_i38.QuestionPart> questionParts;

  final _i38.Question question;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'SubHeaderRouteArgs{key: $key, title: $title, questionParts: $questionParts, question: $question, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i31.TaskDetailsPage]
class TaskDetailsRoute extends _i36.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i37.Key? key,
    required _i38.TaskDetail task,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i31.TaskDetailsPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.task,
  });

  final _i37.Key? key;

  final _i38.TaskDetail task;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i32.TodayPage]
class TodayRoute extends _i36.PageRouteInfo<void> {
  const TodayRoute({List<_i36.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i32.TodayPage();
    },
  );
}

/// generated route for
/// [_i33.UnscheduledPage]
class UnscheduledRoute extends _i36.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i36.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i33.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i34.UsefulLinksPage]
class UsefulLinksRoute extends _i36.PageRouteInfo<void> {
  const UsefulLinksRoute({List<_i36.PageRouteInfo>? children})
      : super(
          UsefulLinksRoute.name,
          initialChildren: children,
        );

  static const String name = 'UsefulLinksRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      return const _i34.UsefulLinksPage();
    },
  );
}

/// generated route for
/// [_i35.WebBrowserPage]
class WebBrowserRoute extends _i36.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i37.Key? key,
    required String url,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i36.PageInfo page = _i36.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i35.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i37.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
