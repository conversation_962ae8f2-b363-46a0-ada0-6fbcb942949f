import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';

import '../../../../config/routes/app_router.gr.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final String userName = "John Doe";
  final String userID = "ID: 75444";
  final String userEmail = "<EMAIL>";

  Future<void> _handleLogout() async {
    final confirmed = await ConfirmDialog.show(
      context: context,
      title: 'Confirm Logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
      onConfirm: () async {
        await sl<DataManager>().clearAll();
        if (mounted) {
          context.router.replaceAll([const LoginRoute()]);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: CustomAppBar(
        title: 'Profile',
        onBackPressed: () {},
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            _buildUserInfoSection(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildGridSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            AppColors.lightGrey1,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Avatar section
          Hero(
            tag: 'profile-avatar',
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primaryBlue.withValues(alpha: 0.1),
                    AppColors.primaryBlue.withValues(alpha: 0.05),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryBlue.withValues(alpha: 0.2),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: CircleAvatar(
                backgroundColor: Colors.transparent,
                radius: 60,
                child: ClipOval(
                  child: Image.network(
                    'https://randomuser.me/api/portraits/men/42.jpg',
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.person,
                          size: 60,
                          color: AppColors.primaryBlue,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),

          // User name
          Text(
            userName,
            style: Theme.of(context).textTheme.montserratheadingmedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.black,
                ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // User ID badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryBlue,
                  AppColors.primaryBlue.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              userID,
              style: Theme.of(context).textTheme.montserratTableSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Email section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.borderColor,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.email_outlined,
                    size: 20,
                    color: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Email Address',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTableSmall
                            .copyWith(
                              color: AppColors.blackTint1,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        userEmail,
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall
                            .copyWith(
                              color: AppColors.black,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridSection() {
    final List<Map<String, dynamic>> menuItems = [
      {
        'title': 'Edit Profile',
        'icon': Icons.edit_outlined,
        'color': const Color(0xFF4A90E2),
        'hasError': true,
        'onTap': () => context.router.push(const EditProfileRoute()),
      },
      {
        'title': 'Availability',
        'icon': Icons.access_time_outlined,
        'color': const Color(0xFF7ED321),
        'hasError': false,
        'onTap': () => context.router.push(const AvailabilityRoute()),
      },
      {
        'title': 'Leave',
        'icon': Icons.event_busy_outlined,
        'color': const Color(0xFFFF9500),
        'hasError': false,
        'onTap': () => context.router.push(const LeaveRoute()),
      },
      {
        'title': 'Skills',
        'icon': Icons.star_outline_rounded,
        'color': const Color(0xFF9013FE),
        'hasError': true,
        'onTap': () => context.router.push(const SkillsRoute()),
      },
      {
        'title': 'Induction',
        'icon': Icons.school_outlined,
        'color': const Color(0xFF00BCD4),
        'hasError': false,
        'onTap': () => context.router.push(const InductionRoute()),
      },
      {
        'title': 'History',
        'icon': Icons.history_outlined,
        'color': const Color(0xFF3F51B5),
        'hasError': false,
        'onTap': () => context.router.push(const HistoryRoute()),
      },
      {
        'title': 'Logout',
        'icon': Icons.logout_outlined,
        'color': const Color(0xFFE53E3E),
        'hasError': false,
        'onTap': () => _handleLogout(),
      },
      {
        'title': 'BI',
        'icon': Icons.bar_chart_outlined,
        'color': const Color(0xFFFF6B35),
        'hasError': false,
        'onTap': () {},
      },
      {
        'title': 'Support',
        'icon': Icons.support_agent_outlined,
        'color': AppColors.primaryBlue,
        'hasError': false,
        'onTap': () {},
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];
        return _buildModernGridButton(
          item['title'],
          item['icon'],
          item['color'],
          item['hasError'],
          item['onTap'],
        );
      },
    );
  }

  Widget _buildModernGridButton(
    String label,
    IconData icon,
    Color color,
    bool hasError,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.15),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Icon container with gradient background
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            color,
                            color.withValues(alpha: 0.7),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        icon,
                        size: 24,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Label
                    Text(
                      label,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .montserratTableSmall
                          .copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                            height: 1.2,
                          ),
                    ),
                  ],
                ),
              ),

              // Error indicator
              if (hasError)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFFF6B6B), Color(0xFFE53E3E)],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFE53E3E).withValues(alpha: 0.4),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.priority_high_rounded,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
