import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';

import '../../../../config/routes/app_router.gr.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final String userName = "John Doe";
  final String userID = "ID: 75444";
  final String userEmail = "<EMAIL>";

  Future<void> _handleLogout() async {
    final confirmed = await ConfirmDialog.show(
      context: context,
      title: 'Confirm Logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
      onConfirm: () async {
        await sl<DataManager>().clearAll();
        if (mounted) {
          context.router.replaceAll([const LoginRoute()]);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      appBar: CustomAppBar(
        title: 'Profile',
        onBackPressed: () {},
      ),
      body: Column(
        children: [
          _buildUserInfoSection(),
          Expanded(
            child: _buildGridSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(
          AppConstants.defaultPadding,
          AppConstants.defaultPadding,
          AppConstants.defaultPadding,
          AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Hero(
            tag: 'profile-avatar',
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: CircleAvatar(
                backgroundColor: AppColors.blackTint2,
                radius: 50,
                child: ClipOval(
                  child: Image.network(
                    'https://randomuser.me/api/portraits/men/42.jpg',
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  userName,
                  style: Theme.of(context)
                      .textTheme
                      .montserratTitleExtraSmall
                      .copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                ),
                const SizedBox(height: 6),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppConstants.defaultBorderRadius),
                  ),
                  child: Text(
                    userID,
                    style: Theme.of(context)
                        .textTheme
                        .montserratTableSmall
                        .copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(top: 3),
                      child: Icon(
                        Icons.email_outlined,
                        size: 16,
                        color: AppColors.blackTint1,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        userEmail,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .montserratTableSmall
                            .copyWith(
                              color: AppColors.blackTint1,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridSection() {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.smallPadding,
          vertical: AppConstants.smallPadding),
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Edit Profile',
                  Icons.edit_outlined,
                  hasError: true,
                  onTap: () {
                    context.router.push(const EditProfileRoute());
                  },
                  color: Colors.blue,
                ),
                _buildGridButton(
                  'Availability',
                  Icons.access_time_outlined,
                  onTap: () {
                    context.router.push(const AvailabilityRoute());
                  },
                  color: Colors.green,
                ),
                _buildGridButton(
                  'Leave',
                  Icons.event_busy_outlined,
                  onTap: () {
                    context.router.push(const LeaveRoute());
                  },
                  color: Colors.orange,
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Skills',
                  Icons.star_outline_rounded,
                  hasError: true,
                  onTap: () {
                    context.router.push(const SkillsRoute());
                  },
                  color: Colors.purple,
                ),
                _buildGridButton(
                  'Induction',
                  Icons.school_outlined,
                  onTap: () {
                    context.router.push(const InductionRoute());
                  },
                  color: Colors.teal,
                ),
                _buildGridButton(
                  'History',
                  Icons.history_outlined,
                  onTap: () {
                    context.router.push(const HistoryRoute());
                  },
                  color: Colors.indigo,
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Logout',
                  Icons.logout_outlined,
                  onTap: () {},
                  color: Colors.red,
                ),
                _buildGridButton(
                  'BI',
                  Icons.bar_chart_outlined,
                  onTap: () {},
                  color: Colors.orange,
                ),
                _buildGridButton(
                  'Support',
                  Icons.support_agent_outlined,
                  onTap: () {},
                  color: AppColors.primaryBlue,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridButton(
    String label,
    IconData icon, {
    bool hasError = false,
    required VoidCallback onTap,
    Color? color,
  }) {
    final buttonColor = color ?? AppColors.primaryBlue;
    // final buttonColor =  Colors.grey.shade800;

    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              spreadRadius: 0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          child: InkWell(
            onTap: label == 'Logout' ? _handleLogout : onTap,
            borderRadius:
                BorderRadius.circular(AppConstants.defaultBorderRadius),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppConstants.smallPadding),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: buttonColor.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          icon,
                          size: 28,
                          color: buttonColor,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        label,
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .montserratTableSmall
                            .copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColors.black,
                            ),
                      ),
                    ],
                  ),
                ),
                if (hasError)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        boxShadow: [
                          // BoxShadow(
                          //   color: Colors.red.withOpacity(0.3),
                          //   blurRadius: 4,
                          //   spreadRadius: 0,
                          // ),
                        ],
                      ),
                      child: const Icon(
                        Icons.priority_high,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
